{% extends 'base.html' %}
{% block body %}
<div class="title">
    <p>قائمة الموردين</p>
    {% set vars = {'total': 0} %}
    {% for task in task %}
    {% if vars.update({'total' : vars.total|int + task.credit}) %} {% endif %}
    {% endfor %}
    <p>TOTAL = {{ vars.total }}</p>
</div>

<div class="form" style="padding-bottom: 1rem">
    <form action="/mwridin/" method="POST">
        <p class="form-title">إضافة مورد</p>
        <div class="first-line">
            <label>
                <span>الإسم :</span>
                <input type="text" required minlength="3" maxlength="25" pattern="^[a-zA-Z0-9]{3,25}$"  name="name" />
            </label>
            <label>
                <span>الهاتف :</span>
                <input type="text" required minlength="10" pattern="^[0-9]*$" name="tele" />
            </label>
            <label>
                <span>الوصف :</span>
                <input type="text" maxlength="200" pattern="^[a-zA-Z0-9]{0,200}$" name="description" />
            </label>
            <label></label>
            <label></label>
            <label>
                <span style="visibility: hidden;">الهاتف :</span>
                <input style="width: 10rem" value="إضافة المورد" type="submit" />
            </label>
        </div>
    </form>
</div>

<div class="mwridin-table" id="print">
    <input type="button" value="الطباعة" class="noprint" onclick="printDiv()"> 
    <table>
        <tr>
            <th>الإسم</th>
            <th>الهاتف</th>
            <th>الوصف</th>
            <th>المبلغ المأتمن</th>
            <th class="noprint">حذف</th>  <!-- عمود الحذف -->
        </tr>

        {% for task in task %}
        <tr>
            <td>
                <a href="/factures/?clnt=1&clnt-id={{task.name}}">
                    {{task.name}}
                </a>
            </td>
            <td>{{task.tele}}</td>
            <td>{{task.description}}</td>
            <td>{{task.credit}} DH</td>
            <td class="noprint">
                <form action="/delete_mwrid/" method="POST" onsubmit="return confirm('هل أنت متأكد من حذف هذا المورد؟');">
                    <input type="hidden" name="name" value="{{ task.name }}">
                    <input type="submit" value="🗑 حذف">
                </form>
            </td>
        </tr>
        {% endfor %}
    </table>
</div>
{% endblock %}
