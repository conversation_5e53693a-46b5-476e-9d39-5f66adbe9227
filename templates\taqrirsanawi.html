{% extends 'base.html' %}
{% block body %}

<!-- أزرار التحكم في الوضع -->
<div class="control-buttons">
    <button id="themeToggle" class="theme-btn">🌙 الوضع الليلي</button>
    <button id="printBtn" class="print-btn">🖨️ طباعة التقرير</button>
</div>

<!-- خلفية النجوم المتحركة -->
<div class="stars-container" id="starsContainer">
    <div class="stars"></div>
    <div class="stars2"></div>
    <div class="stars3"></div>
</div>

<div class="title">
    <p>التقرير السنوي</p>
</div>

<!-- قسم البحث -->
<div class="form">
    <form action="/taqrirsanawi/" method="POST">
        <div class="first-line">
            <label>
                <span>صاحب الفاتورة:</span>
                <select class="js-client-select" name="client">
                    <option>TOUT_VENTES</option>
                    {% for clients in clients %}
                    <option>{{ clients.name }}</option>
                    {% endfor %}
                    {% for mowaridon in mowaridon %}
                    <option>{{ mowaridon.name }}</option>
                    {% endfor %}
                </select>
            </label>
            <label>
                <span>السنة:</span>
                <input name='year' placeholder="2024" type='number' min='2015' max='2600'>
            </label>
            <label>
                <input type="submit" value="إبحث عن التقرير" />
            </label>
        </div>
    </form>
</div>





<!-- التقرير السنوي البسيط -->
<!-- سيظهر التقرير دائماً -->
<div class="annual-report" id="print">
    <!-- معلومات التقرير -->
    <div style="text-align: center; margin-bottom: 20px; font-size: 16px;">
        <p><strong>العميل:</strong> {{ selected_client }}</p>
        <p><strong>السنة:</strong> {{ selected_year }}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th colspan="3">فواتير البيع</th>
                <th colspan="3">فواتير الاستخلاص</th>
            </tr>
            <tr>
                <th>رقم الفاتورة</th>
                <th>المبلغ</th>
                <th>التاريخ</th>
                <th>رقم الفاتورة</th>
                <th>المبلغ</th>
                <th>التاريخ</th>
            </tr>
        </thead>
        <tbody>
            {% set sales_list = sales_data|list if sales_data else [] %}
            {% set extraction_list = extraction_data|list if extraction_data else [] %}
            {% set max_rows = [sales_list|length, extraction_list|length]|max %}
            
            {% if max_rows == 0 %}
            <!-- رسالة عندما لا توجد بيانات -->
            <tr>
                <td colspan="6" style="padding: 20px; text-align: center; color: #666; font-style: italic;">
                    لا توجد فواتير لهذا العميل في السنة المحددة
                </td>
            </tr>
            {% else %}
            
            {% for i in range(max_rows) %}
            <tr>
                <!-- فواتير البيع -->
                {% if i < sales_list|length %}
                <td>
                    <a href="/factures/?fact=1&fact-id={{ sales_list[i].id }}">
                        {{ sales_list[i].id }}
                    </a>
                </td>
                <td>{{ "%.0f"|format(sales_list[i].total) }}</td>
                <td>{{ sales_list[i].date }}</td>
                {% else %}
                <td>-</td>
                <td>-</td>
                <td>-</td>
                {% endif %}
                
                <!-- فواتير الاستخلاص -->
                {% if i < extraction_list|length %}
                <td>
                    <a href="/factures/?fact=1&fact-id={{ extraction_list[i].id }}">
                        {{ extraction_list[i].id }}
                    </a>
                </td>
                <td>{{ "%.0f"|format(extraction_list[i].total) }}</td>
                <td>{{ extraction_list[i].date }}</td>
                {% else %}
                <td>-</td>
                <td>-</td>
                <td>-</td>
                {% endif %}
            </tr>
            {% endfor %}
            {% endif %}
        </tbody>
        <tfoot>
            <tr>
                <td colspan="2">مجموع البيع</td>
                <td>{{ "%.0f"|format(total_sales) if total_sales is defined else '0' }}</td>
                <td colspan="2">مجموع الاستخلاص</td>
                <td>{{ "%.0f"|format(total_extraction) if total_extraction is defined else '0' }}</td>
            </tr>
            <tr>
                <td colspan="3">الدين المتبقي</td>
                <td colspan="3">
                    {% set remaining = (total_sales|default(0) - total_extraction|default(0)) %}
                    {{ "%.0f"|format(remaining) }}
                </td>
            </tr>
        </tfoot>
    </table>
</div>

<style>
    /* أزرار التحكم */
    .control-buttons {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        display: flex;
        gap: 10px;
    }
    
    .theme-btn, .print-btn {
        padding: 12px 20px;
        border: none;
        border-radius: 25px;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .theme-btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
    }
    
    .print-btn {
        background: linear-gradient(45deg, #11998e, #38ef7d);
        color: white;
    }
    
    .theme-btn:hover, .print-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }
    
    /* خلفية النجوم المتحركة */
    .stars-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: #000;
        opacity: 0;
        transition: opacity 0.5s ease;
    }
    
    .stars-container.night-mode {
        opacity: 1;
    }
    
    .stars, .stars2, .stars3 {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: transparent;
    }
    
    .stars {
        background-image: radial-gradient(2px 2px at 20px 30px, #eee, transparent),
                          radial-gradient(2px 2px at 40px 70px, #fff, transparent),
                          radial-gradient(1px 1px at 90px 40px, #eee, transparent),
                          radial-gradient(1px 1px at 130px 80px, #fff, transparent),
                          radial-gradient(2px 2px at 160px 30px, #fff, transparent);
        background-repeat: repeat;
        background-size: 200px 100px;
        animation: twinkle 2s linear infinite;
    }
    
    .stars2 {
        background-image: radial-gradient(1px 1px at 40px 60px, #fff, transparent),
                          radial-gradient(1px 1px at 80px 10px, #eee, transparent),
                          radial-gradient(2px 2px at 120px 50px, #fff, transparent),
                          radial-gradient(1px 1px at 170px 80px, #eee, transparent);
        background-repeat: repeat;
        background-size: 200px 100px;
        animation: twinkle 3s linear infinite;
    }
    
    .stars3 {
        background-image: radial-gradient(1px 1px at 10px 20px, #fff, transparent),
                          radial-gradient(2px 2px at 60px 90px, #eee, transparent),
                          radial-gradient(1px 1px at 100px 60px, #fff, transparent),
                          radial-gradient(1px 1px at 140px 20px, #eee, transparent);
        background-repeat: repeat;
        background-size: 200px 100px;
        animation: twinkle 4s linear infinite;
    }
    
    @keyframes twinkle {
        0%, 100% { opacity: 0.3; }
        50% { opacity: 1; }
    }
    
    /* تأثيرات الوضع الليلي */
    body.night-mode {
        background-color: #1a1a1a;
        color: #fff;
        transition: all 0.5s ease;
    }
    
    body.night-mode .title p {
        color: #fff;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    }
    
    body.night-mode .form {
        background-color: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    body.night-mode .form label span {
        color: #fff;
    }
    
    body.night-mode .form select, 
    body.night-mode .form input[type="number"] {
        background-color: rgba(255, 255, 255, 0.1);
        color: #fff;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    body.night-mode .form input[type="submit"] {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border: none;
    }
    
    body.night-mode .annual-report {
        background-color: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    body.night-mode .annual-report table {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        overflow: hidden;
    }
    
    body.night-mode .annual-report th {
        background-color: rgba(255, 255, 255, 0.2) !important;
        color: #fff;
        border-color: rgba(255, 255, 255, 0.3);
    }
    
    body.night-mode .annual-report td {
        background-color: rgba(255, 255, 255, 0.05);
        color: #fff;
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    body.night-mode .annual-report tfoot td {
        background-color: rgba(255, 255, 255, 0.15) !important;
        color: #fff;
    }
    
    body.night-mode .annual-report a {
        color: #64ffda;
    }
    
    body.night-mode .annual-report a:hover {
        color: #1de9b6;
        text-shadow: 0 0 5px rgba(29, 233, 182, 0.5);
    }

    .annual-report {
        margin: 20px auto;
        max-width: 1200px;
        position: relative;
        z-index: 1;
    }
    
    .annual-report table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 30px;
        font-family: 'Arial', sans-serif;
    }
    
    .annual-report th, 
    .annual-report td {
        border: 1px solid #000;
        padding: 8px;
        text-align: center;
    }
    
    .annual-report thead tr:first-child th {
        background-color: #f2f2f2;
        font-weight: bold;
        font-size: 16px;
    }
    
    .annual-report thead tr:last-child th {
        background-color: #f2f2f2;
        font-weight: bold;
    }
    
    .annual-report tfoot td {
        font-weight: bold;
        background-color: #e6e6e6;
        font-size: 14px;
    }
    
    .annual-report a {
        color: var(--main);
        text-decoration: none;
        font-weight: bold;
    }
    
    .annual-report a:hover {
        text-decoration: underline;
    }
    
    /* تنسيق الطباعة */
    @media print {
        @page {
            size: A4 landscape;
            margin: 1.5cm;
        }
        
        .noprint {
            display: none !important;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            background-color: #fff;
            margin: 0;
            font-size: 12pt;
        }
        
        .title p {
            text-align: center;
            font-size: 20pt;
            margin-bottom: 20px;
            font-weight: bold;
            color: black;
        }
        
        .annual-report table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 11pt;
        }
        
        .annual-report th, 
        .annual-report td {
            border: 1px solid #000;
            padding: 6pt;
            text-align: center;
        }
        
        .annual-report thead th {
            background-color: #f2f2f2 !important;
            font-weight: bold;
        }
        
        .annual-report tfoot td {
            font-weight: bold;
            background-color: #e6e6e6 !important;
        }
        
        .annual-report a {
            color: black;
            text-decoration: none;
            font-weight: bold;
        }
    }
</style>

<script>
    // متغيرات الحالة
    let isNightMode = false;
    
    // تهيئة الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        const themeToggle = document.getElementById('themeToggle');
        const printBtn = document.getElementById('printBtn');
        const starsContainer = document.getElementById('starsContainer');
        
        // زر تغيير الوضع
        themeToggle.addEventListener('click', function() {
            isNightMode = !isNightMode;
            
            if (isNightMode) {
                // تفعيل الوضع الليلي
                document.body.classList.add('night-mode');
                starsContainer.classList.add('night-mode');
                themeToggle.innerHTML = '☀️ الوضع العادي';
                
                // تأثير صوتي (اختياري)
                playSound('night');
            } else {
                // تفعيل الوضع العادي
                document.body.classList.remove('night-mode');
                starsContainer.classList.remove('night-mode');
                themeToggle.innerHTML = '🌙 الوضع الليلي';
                
                // تأثير صوتي (اختياري)
                playSound('day');
            }
            
            // حفظ الحالة في التخزين المحلي
            localStorage.setItem('nightMode', isNightMode);
        });
        
        // زر الطباعة
        printBtn.addEventListener('click', function() {
            printReport();
        });
        
        // استعادة الحالة المحفوظة
        const savedTheme = localStorage.getItem('nightMode');
        if (savedTheme === 'true') {
            themeToggle.click();
        }
        
        // تأثيرات إضافية للأزرار
        addButtonEffects();
    });
    
    // دالة الطباعة المحسنة
    function printReport() {
        const originalTitle = document.title;
        document.title = 'التقرير السنوي - {{ selected_client }} - {{ selected_year }}';
        
        const printContents = document.getElementById('print').outerHTML;
        const titleContent = document.querySelector('.title').outerHTML;
        
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html lang="ar" dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>التقرير السنوي</title>
                <style>
                    @page { size: A4 landscape; margin: 1.5cm; }
                    body { 
                        font-family: 'Arial', sans-serif; 
                        direction: rtl; 
                        background-color: #fff; 
                        margin: 20px; 
                        font-size: 12pt; 
                        color: #000 !important;
                    }
                    .title { 
                        text-align: center; 
                        font-size: 20pt; 
                        margin-bottom: 20px; 
                        font-weight: bold;
                        color: #000 !important;
                    }
                    table { 
                        width: 100%; 
                        border-collapse: collapse; 
                        margin-top: 30px; 
                    }
                    th, td { 
                        border: 1px solid #000; 
                        padding: 8px; 
                        text-align: center; 
                        color: #000 !important;
                    }
                    thead th { 
                        background-color: #f2f2f2 !important; 
                        font-weight: bold;
                    }
                    tfoot td { 
                        font-weight: bold; 
                        background-color: #e6e6e6 !important; 
                    }
                    a { 
                        color: #000 !important; 
                        text-decoration: none; 
                        font-weight: bold; 
                    }
                    .control-buttons { display: none !important; }
                    .stars-container { display: none !important; }
                </style>
            </head>
            <body>
                ${titleContent}
                ${printContents}
                <div style="text-align: center; margin-top: 30px; font-size: 10pt; color: #666;">
                    تاريخ الطباعة: ${new Date().toLocaleString('ar-EG')} | 
                    تم إنشاؤه بواسطة نظام إدارة المبيعات
                </div>
            </body>
            </html>
        `);
        
        printWindow.document.close();
        setTimeout(() => {
            printWindow.focus();
            printWindow.print();
            printWindow.close();
            document.title = originalTitle;
        }, 500);
        
        // تأثير بصري لإشعار المستخدم
        showNotification('📄 تم إرسال التقرير للطباعة');
    }
    
    // إضافة تأثيرات للأزرار
    function addButtonEffects() {
        const buttons = document.querySelectorAll('.theme-btn, .print-btn');
        
        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.05)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
            
            button.addEventListener('click', function() {
                // تأثير النقر
                this.style.transform = 'translateY(1px) scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-2px) scale(1.05)';
                }, 100);
            });
        });
    }
    
    // دالة التأثيرات الصوتية (اختيارية)
    function playSound(type) {
        // يمكن إضافة ملفات صوتية هنا
        try {
            const audio = new Audio();
            if (type === 'night') {
                // صوت التبديل للوضع الليلي
                audio.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJKSj5I=';
            } else {
                // صوت التبديل للوضع العادي
                audio.src = 'data:audio/wav;base64,UklGRvIGAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YU4GAACB=';
            }
            audio.volume = 0.1;
            audio.play().catch(() => {}); // تجاهل الأخطاء
        } catch (e) {
            // تجاهل أخطاء الصوت
        }
    }
    
    // دالة إظهار الإشعارات
    function showNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: linear-gradient(45deg, #11998e, #38ef7d);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            z-index: 10000;
            font-weight: bold;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transform: translateX(300px);
            transition: transform 0.3s ease;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);
        
        // إظهار الإشعار
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // إخفاء الإشعار
        setTimeout(() => {
            notification.style.transform = 'translateX(300px)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    // تأثيرات إضافية للنجوم عند التحريك
    document.addEventListener('mousemove', function(e) {
        if (isNightMode) {
            const stars = document.querySelectorAll('.stars, .stars2, .stars3');
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;
            
            stars.forEach((star, index) => {
                const speed = (index + 1) * 0.5;
                star.style.transform = `translate(${mouseX * speed}px, ${mouseY * speed}px)`;
            });
        }
    });
</script>

{% endblock %}
