#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص هيكل قاعدة البيانات
"""

import sqlite3
import os

def check_database():
    db_path = 'database.db'
    
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود!")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # جلب أسماء الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("📊 جداول قاعدة البيانات:")
        print("=" * 40)
        
        for table in tables:
            table_name = table[0]
            print(f"\n🗂️  جدول: {table_name}")
            
            # جلب هيكل الجدول
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            print("   الأعمدة:")
            for col in columns:
                col_name = col[1]
                col_type = col[2]
                print(f"   - {col_name} ({col_type})")
            
            # جلب عدد السзаписи
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"   📈 عدد السجلات: {count}")
            
            # عرض بعض البيانات النموذجية
            if count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3;")
                rows = cursor.fetchall()
                print("   🔍 بيانات نموذجية:")
                for i, row in enumerate(rows, 1):
                    print(f"   {i}. {row}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")

if __name__ == "__main__":
    check_database()