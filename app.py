#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask App لتشغيل صفحة التقرير السنوي
"""

from flask import Flask, render_template, request
from datetime import datetime
import sqlite3
import os

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# إعداد قاعدة البيانات
DATABASE_PATH = 'database.db'

def get_db_connection():
    """إنشاء اتصال بقاعدة البيانات"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row  # لإرجاع النتائج كقاموس
    return conn

def get_clients():
    """جلب قائمة العملاء من قاعدة البيانات"""
    try:
        conn = get_db_connection()
        clients = conn.execute('SELECT name FROM client ORDER BY name').fetchall()
        conn.close()
        return [{'name': client['name']} for client in clients]
    except Exception as e:
        print(f"خطأ في جلب العملاء: {e}")
        return []

def get_suppliers():
    """جلب قائمة الموردين من قاعدة البيانات"""
    try:
        conn = get_db_connection()
        suppliers = conn.execute('SELECT name FROM mowarid ORDER BY name').fetchall()
        conn.close()
        return [{'name': supplier['name']} for supplier in suppliers]
    except Exception as e:
        print(f"خطأ في جلب الموردين: {e}")
        return []

def get_sales_data(client_name, year=None):
    """جلب فواتير البيع من قاعدة البيانات"""
    try:
        conn = get_db_connection()
        
        if year:
            # البحث بالعميل والسنة
            query = """
                SELECT id, total, date, client 
                FROM logs 
                WHERE client = ? AND typeVA = 'V' AND date LIKE ?
                ORDER BY date DESC
            """
            sales = conn.execute(query, (client_name, f'{year}%')).fetchall()
        else:
            # البحث بالعميل فقط
            query = """
                SELECT id, total, date, client 
                FROM logs 
                WHERE client = ? AND typeVA = 'V'
                ORDER BY date DESC
            """
            sales = conn.execute(query, (client_name,)).fetchall()
        
        conn.close()
        
        # تحويل النتائج إلى قائمة قواميس
        sales_list = []
        for sale in sales:
            sales_list.append({
                'id': sale['id'],
                'total': sale['total'] or 0,
                'date': sale['date'],
                'client': sale['client']
            })
        
        return sales_list
        
    except Exception as e:
        print(f"خطأ في جلب فواتير البيع: {e}")
        return []

def get_extraction_data(client_name, year=None):
    """جلب فواتير الاستخلاص من قاعدة البيانات"""
    try:
        conn = get_db_connection()
        
        if year:
            # البحث بالعميل والسنة
            query = """
                SELECT id, amount as total, date, client 
                FROM stikhlaslog 
                WHERE client = ? AND date LIKE ?
                ORDER BY date DESC
            """
            extractions = conn.execute(query, (client_name, f'{year}%')).fetchall()
        else:
            # البحث بالعميل فقط
            query = """
                SELECT id, amount as total, date, client 
                FROM stikhlaslog 
                WHERE client = ? 
                ORDER BY date DESC
            """
            extractions = conn.execute(query, (client_name,)).fetchall()
        
        conn.close()
        
        # تحويل النتائج إلى قائمة قواميس
        extraction_list = []
        for extraction in extractions:
            extraction_list.append({
                'id': extraction['id'],
                'total': extraction['total'] or 0,
                'date': extraction['date'],
                'client': extraction['client']
            })
        
        return extraction_list
        
    except Exception as e:
        print(f"خطأ في جلب فواتير الاستخلاص: {e}")
        return []

def get_all_sales_data(year=None):
    """جلب جميع فواتير البيع"""
    try:
        conn = get_db_connection()
        
        if year:
            query = """
                SELECT id, total, date, client 
                FROM logs 
                WHERE typeVA = 'V' AND date LIKE ?
                ORDER BY date DESC
            """
            sales = conn.execute(query, (f'{year}%',)).fetchall()
        else:
            query = """
                SELECT id, total, date, client 
                FROM logs 
                WHERE typeVA = 'V'
                ORDER BY date DESC
            """
            sales = conn.execute(query).fetchall()
        
        conn.close()
        
        sales_list = []
        for sale in sales:
            sales_list.append({
                'id': sale['id'],
                'total': sale['total'] or 0,
                'date': sale['date'],
                'client': sale['client']
            })
        
        return sales_list
        
    except Exception as e:
        print(f"خطأ في جلب جميع فواتير البيع: {e}")
        return []

def get_all_extraction_data(year=None):
    """جلب جميع فواتير الاستخلاص"""
    try:
        conn = get_db_connection()
        
        if year:
            query = """
                SELECT id, amount as total, date, client 
                FROM stikhlaslog 
                WHERE date LIKE ?
                ORDER BY date DESC
            """
            extractions = conn.execute(query, (f'{year}%',)).fetchall()
        else:
            query = """
                SELECT id, amount as total, date, client 
                FROM stikhlaslog 
                ORDER BY date DESC
            """
            extractions = conn.execute(query).fetchall()
        
        conn.close()
        
        extraction_list = []
        for extraction in extractions:
            extraction_list.append({
                'id': extraction['id'],
                'total': extraction['total'] or 0,
                'date': extraction['date'],
                'client': extraction['client']
            })
        
        return extraction_list
        
    except Exception as e:
        print(f"خطأ في جلب جميع فواتير الاستخلاص: {e}")
        return []

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/taqrirsanawi/', methods=['GET', 'POST'])
def annual_report():
    """صفحة التقرير السنوي"""
    
    # جلب البيانات من قاعدة البيانات
    clients = get_clients()
    suppliers = get_suppliers()
    
    # البيانات الافتراضية
    context = {
        'clients': clients,
        'mowaridon': suppliers,
        'selected_client': None,
        'selected_year': None,
        'sales_data': [],
        'extraction_data': [],
        'total_sales': 0,
        'total_extraction': 0,
        'current_date': datetime.now().strftime('%Y-%m-%d')
    }
    
    if request.method == 'POST':
        # الحصول على البيانات من النموذج
        selected_client = request.form.get('client', '')
        selected_year = request.form.get('year', '')
        
        print(f"🔍 تم البحث عن: العميل={selected_client}, السنة={selected_year}")
        
        # تحديث السياق
        context.update({
            'selected_client': selected_client,
            'selected_year': selected_year
        })
        
        # جلب البيانات من قاعدة البيانات
        if selected_client and selected_client != 'TOUT_VENTES':
            # جلب بيانات عميل محدد
            sales_data = get_sales_data(selected_client, selected_year)
            extraction_data = get_extraction_data(selected_client, selected_year)
            
            # حساب المجاميع
            total_sales = sum(sale['total'] for sale in sales_data)
            total_extraction = sum(extraction['total'] for extraction in extraction_data)
            
            # تحديث السياق
            context.update({
                'sales_data': sales_data,
                'extraction_data': extraction_data,
                'total_sales': total_sales,
                'total_extraction': total_extraction
            })
            
            print(f"📊 النتائج: {len(sales_data)} فواتير بيع, {len(extraction_data)} فواتير استخلاص")
            print(f"💰 المجاميع: البيع={total_sales}, الاستخلاص={total_extraction}")
        
        elif selected_client == 'TOUT_VENTES':
            # جلب جميع الفواتير
            sales_data = get_all_sales_data(selected_year)
            extraction_data = get_all_extraction_data(selected_year)
            
            # حساب المجاميع
            total_sales = sum(sale['total'] for sale in sales_data)
            total_extraction = sum(extraction['total'] for extraction in extraction_data)
            
            context.update({
                'sales_data': sales_data,
                'extraction_data': extraction_data,
                'total_sales': total_sales,
                'total_extraction': total_extraction
            })
            print(f"📊 عرض جميع الفواتير: {len(sales_data)} بيع, {len(extraction_data)} استخلاص")
            print(f"💰 إجمالي المجاميع: البيع={total_sales}, الاستخلاص={total_extraction}")
    
    return render_template('taqrirsanawi.html', **context)

@app.route('/3omalae/', methods=['GET', 'POST'])
def clients_page():
    """صفحة إدارة العملاء"""
    
    if request.method == 'POST':
        action = request.form.get('action')
        
        if action == 'add':
            # إضافة عميل جديد
            name = request.form.get('name')
            city = request.form.get('city', '')
            tele = request.form.get('tele')
            cin = request.form.get('cin', '')
            description = request.form.get('description', '')
            
            try:
                conn = get_db_connection()
                conn.execute('''
                    INSERT INTO client (name, city, tele, cin, description, credit)
                    VALUES (?, ?, ?, ?, ?, 0)
                ''', (name, city, tele, cin, description))
                conn.commit()
                conn.close()
                print(f"✅ تم إضافة العميل: {name}")
            except Exception as e:
                print(f"❌ خطأ في إضافة العميل: {e}")
        
        elif action == 'delete':
            # حذف عميل
            client_name = request.form.get('client')
            try:
                conn = get_db_connection()
                conn.execute('DELETE FROM client WHERE name = ?', (client_name,))
                conn.commit()
                conn.close()
                print(f"✅ تم حذف العميل: {client_name}")
            except Exception as e:
                print(f"❌ خطأ في حذف العميل: {e}")
    
    # جلب جميع العملاء مع حساب الرصيد
    try:
        conn = get_db_connection()
        
        # جلب جميع العملاء
        clients = conn.execute('''
            SELECT name, city, tele, cin, description, credit
            FROM client 
            ORDER BY name
        ''').fetchall()
        
        # حساب الرصيد لكل عميل
        clients_with_balance = []
        for client in clients:
            client_name = client['name']
            
            # حساب إجمالي المبيعات
            total_sales = conn.execute('''
                SELECT COALESCE(SUM(total), 0) as total
                FROM logs 
                WHERE client = ? AND typeVA = 'V'
            ''', (client_name,)).fetchone()['total']
            
            # حساب إجمالي الاستخلاص
            total_extractions = conn.execute('''
                SELECT COALESCE(SUM(amount), 0) as total
                FROM stikhlaslog 
                WHERE client = ?
            ''', (client_name,)).fetchone()['total']
            
            # حساب الرصيد المتبقي
            balance = total_sales - total_extractions
            
            clients_with_balance.append({
                'name': client['name'],
                'city': client['city'] or '',
                'tele': client['tele'] or '',
                'cin': client['cin'] or '',
                'description': client['description'] or '',
                'credit': balance
            })
        
        conn.close()
        
        return render_template('3omalae.html', task=clients_with_balance)
        
    except Exception as e:
        print(f"❌ خطأ في جلب العملاء: {e}")
        return render_template('3omalae.html', task=[])

@app.route('/factures/')
def factures():
    """صفحة الفواتير (للروابط في التقرير)"""
    fact_id = request.args.get('fact-id')
    clnt_id = request.args.get('clnt-id')
    if clnt_id:
        return f"<h1>فواتير العميل: {clnt_id}</h1><p>هذه صفحة تجريبية لعرض فواتير العميل</p>"
    return f"<h1>تفاصيل الفاتورة رقم: {fact_id}</h1><p>هذه صفحة تجريبية لعرض تفاصيل الفاتورة</p>"

@app.route('/3omalae')
def workers():
    # جلب بيانات العمال من قاعدة البيانات
    conn = get_db_connection()
    workers = conn.execute('SELECT * FROM workers').fetchall()
    conn.close()
    return render_template('3omalae.html', workers=workers)

@app.route('/mochtaryat')
def purchases():
    # جلب بيانات المشتريات
    conn = get_db_connection()
    purchases = conn.execute('SELECT * FROM purchases').fetchall()
    conn.close()
    return render_template('mochtarayat.html', purchases=purchases)

@app.route('/mwridin')
def suppliers():
    suppliers = get_suppliers()
    return render_template('mwridin.html', suppliers=suppliers)

if __name__ == '__main__':
    print("🚀 بدء تشغيل Flask Server...")
    print("🌐 رابط التطبيق: http://localhost:5000")
    print("📋 رابط التقرير السنوي: http://localhost:5000/taqrirsanawi/")
    print("⏹️ لإيقاف الخادم: اضغط Ctrl+C")
    print("-" * 50)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=True
    )