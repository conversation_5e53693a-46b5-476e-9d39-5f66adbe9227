{% extends 'base.html' %}
{% block body %}
            <h1 class="main-page__title">COLENDRIER AGRICOLE</h1>
            <div class="title">
                {% set vars = {'total': 0|float, 'total2': 0|float, 'total3': 0|float} %}
                {% for task in task %}
                {% if vars.update({'total' : vars.total|float + task.total|float}) %} {% endif %}
                {% endfor %}
                {% for task2 in task2 %}
                {% if vars.update({'total2' : vars.total2|float + task2.credit|float}) %} {% endif %}
                {% endfor %}
                {% for task3 in task3 %}
                {% if vars.update({'total3' : vars.total3|float + task3.credit|float}) %} {% endif %}
                {% endfor %}
                <p>المخزون = {{ vars.total }}</p>
                <p>الموردون = {{ vars.total2 }}</p>
                <p>العملاء  = {{ vars.total3 }}</p>
                <p>الخزينة = {{ caise }}</p>
            </div>
            <div class="form"  >
                <form  target="" method="POST">
                    <div class="first-line">
                        <label>
                            <input type=number placeholder='0' name="value">
                        </label>
                        <label>
                            <input type="submit" value=" الدفع " />
                        </label>
                    </div>
                </form>
            </div>
{% endblock %}