<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <title>Calendrier Agricole</title>
</head>
<body class="facture-page">
    <div class="prfloat" onClick="prfloatPage()"></div>
    <div class="content-wrapper">
        <div class="logo">
            <img src="{{ url_for('static', filename='img/LOGO.png') }}" />
        </div>
        <div class="info flex-container" lang="ar" dir="rtl">
            {% if task.typeVA == 'V' %}
            <h3>فاتورة المبيعات</h3>
            {% endif %}
            {% if task.typeVA == 'A' %}
            <h3>فاتورة المشتريات</h3>
            {% endif %}
            <div class="flex1">
                <p>رقم الفاتورة :{{task.id}}</p>
                <p>تاريخ الفاتورة :{{task.date}}</p>
            </div>
            <div class="flex2">
                <p>طريقة الدفع :{{task.type_transaction}}</p>
                <p>إسم العميل :{{task.client}}</p>
            </div>
        </div>
        {{task.typeVA}}
        <div class="table" lang="ar" dir="rtl">
            <table>
                <tr>
                    <th>اسم الصنف</th>
                    <th>الكمية</th>
                    <th>سعر البيع</th>
                    <th>المجموع</th>
                </tr>
                <tr>
                    <td>{{task.sinf_1}}</td>
                    <td>{{task.quantity_1}}</td>
                    <td>{{task.prix_1}}</td>
                    <td>{{task.quantity_1|float * task.prix_1|float}}DH</td>
                </tr>
                <tr>
                    <td>{{task.sinf_2}}</td>
                    <td>{{task.quantity_2}}</td>
                    <td>{{task.prix_2}}</td>
                    <td>{{task.quantity_2|float * task.prix_2|float}}DH</td>
                </tr>
                <tr>
                    <td>{{task.sinf_3}}</td>
                    <td>{{task.quantity_3}}</td>
                    <td>{{task.prix_3}}</td>
                    <td>{{task.quantity_3|float * task.prix_3|float}}DH</td>
                </tr>
                <tr>
                    <td>{{task.sinf_4}}</td>
                    <td>{{task.quantity_4}}</td>
                    <td>{{task.prix_4}}</td>
                    <td>{{task.quantity_4|float * task.prix_4|float}}DH</td>
                </tr>
                <tr>
                    <td>{{task.sinf_5}}</td>
                    <td>{{task.quantity_5}}</td>
                    <td>{{task.prix_5}}</td>
                    <td>{{task.quantity_5|float * task.prix_5|float}}DH</td>
                </tr>
                <tr>
                    <td>{{task.sinf_6}}</td>
                    <td>{{task.quantity_6}}</td>
                    <td>{{task.prix_6}}</td>
                    <td>{{task.quantity_6|float * task.prix_6|float}}DH</td>
                </tr>
                <tr>
                    <td>{{task.sinf_7}}</td>
                    <td>{{task.quantity_7}}</td>
                    <td>{{task.prix_7}}</td>
                    <td>{{task.quantity_7|float * task.prix_7|float}}DH</td>
                </tr>

            </table>
        </div>
        <div class="ijmaly" lang="ar" dir="rtl">
            <table>
                <tr>
                    <th>إجمالي الفاتورة :</th>
                    <td>{{task.total}} DH</td>
                </tr>
            </table>
        </div>
        <p lang="ar" dir="rtl" class="signatures">التواقيع :</p>
        <div class="footer" dir="ltr">
            <p style="color: white;">Ste Calendrier Agricole s.a.r.l - AU au capital 100 000.0 Dhs Siege Social : Rond-pofloat de Qallalsh - Mnasra Kenitra</p>
            <p style="color: white;">RC : 55349 - I.F 459 365 39 - Patente : 208 012 11 - ICE 002590487000059</p>
            <p style="color: white;">Tel : 06 66 33 22 69 - Email <EMAIL> - Face : calendrier agricol</p>
        </div>
    </div>
</body>
<script src="{{ url_for('static', filename='js/main.js') }}"></script>
</html>