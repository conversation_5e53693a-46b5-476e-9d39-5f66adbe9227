{% extends 'base.html' %}
{% block body %}
<div class="title">
    <p>قائمة الفواتير</p>
</div>
<div class="form" style="padding-bottom: 1rem; display: flex; align-items: center" action="/factures" method="GET">
    <form style="width: 50%; display: flex; align-items: center; flex-direction: column;">
        <input type="hidden" name="fact" value="1">
        <p class="form-title">البحث عن فاتورة</p>
        <div class="square" style="justify-content: flex-start; padding-right: 2rem;">
            <label>
                <span>رقم الفاتورة :</span>
                <input type="number" value="0" required name="fact-id" />
            </label>
            <label>
                <span style="visibility: hidden">رقم الفاتورة :</span>
                <input type="submit" value="إبحث عن الفاتورة" />
            </label>
        </div>
    </form>
    <form style="width: 50%; display: flex; align-items: center; flex-direction: column;" action="/factures" method="GET">
        <input type="hidden" name="clnt" value="1">
        <p class="form-title">البحث عن العميل</p>
        <div class="square">
            <label>
                <span>إسم صاحب الفاتورة :</span>
                <select class="js-client-select" name="clnt-id">
                    {% for clients in clients %}
                    <option>{{ clients.name }}</option>
                    {% endfor %}
                    {% for mowaridon in mowaridon %}
                    <option>{{ mowaridon.name }}</option>
                    {% endfor %}

                </select>
            </label>
            <label>
                <span style="visibility: hidden">إسم العميل :</span>
                <input type="submit" value="إبحث عن الفاتورة" />
            </label>
        </div>
    </form>
</div>
<div class="results">
    <p class="error hidden">لا توجد اي فاتورة بهذا الرقم.</p>
    <div class="facture-table" id="print">
        <input  type="button" value="الطباعة" class="noprint" onclick="printDiv()" > 
        <table style="display:block;max-height:200rem;overflow-y:scroll;width:fit-content">
            <tr>
                <th>رقم الفاتورة</th>
                <th>نوع الفاتورة</th>
                <th>العميل/المورد</th>
                <th>الأصناف</th>
                <th>المجموع</th>
                <th>طريقة الاداء</th>
                <th>التاريخ</th>
            </tr>
{% for tasks in tasks|reverse %}
            <tr>
                <td>
                    <a >
                        {{ tasks.id }}
                    </a>
                </td>
                <td>{{ tasks.typeVA }}</td>
                <td>
                    <a >
                        {{ tasks.client }}
                    </a>
                </td>
                <td style="line-height: 1.7rem;">
                    {% if tasks.prix_1|float > 0 %}
                    <p>
                        {{ tasks.quantity_1|float * tasks.prix_1|float }} = {{tasks.quantity_1}}x  {{tasks.prix_1}} {{tasks.sinf_1}} 
                    </p>
                    {% endif %}
                    {% if tasks.prix_2|float > 0 %}
                    <p>
                        {{ tasks.quantity_2|float * tasks.prix_2|float }} = {{tasks.quantity_2}}  {{tasks.prix_2}} {{tasks.sinf_2}} 
                    </p>
                    {% endif %}
                    {% if tasks.prix_3|float > 0 %}
                    <p>
                        {{ tasks.quantity_3|float * tasks.prix_3|float }} = {{tasks.quantity_3}}  {{tasks.prix_3}} {{tasks.sinf_3}} 
                    </p>
                    {% endif %}
                    {% if tasks.prix_4|float > 0 %}
                    <p>
                        {{ tasks.quantity_4|float * tasks.prix_4|float }} = {{tasks.quantity_4}}  {{tasks.prix_4}} {{tasks.sinf_4}} 
                    </p>
                    {% endif %}
                    {% if tasks.prix_5|float > 0 %}
                    <p>
                        {{ tasks.quantity_5|float * tasks.prix_5|float }} = {{tasks.quantity_5}}  {{tasks.prix_5}} {{tasks.sinf_5}} 
                    </p>
                    {% endif %}
                    {% if tasks.prix_6|float > 0 %}
                    <p>
                        {{ tasks.quantity_6|float * tasks.prix_6|float }} = {{tasks.quantity_6}}  {{tasks.prix_6}} {{tasks.sinf_6}} 
                    </p>
                    {% endif %}
                    {% if tasks.prix_7|float > 0 %}
                    <p>
                        {{ tasks.quantity_7|float * tasks.prix_7|float }} = {{tasks.quantity_7}}  {{tasks.prix_7}} {{tasks.sinf_7}} 
                    </p>
                    {% endif %}
                </td>
                <td>{{tasks.total}}DH</td>
                <td>{{tasks.type_transaction}}</td>
                <td>{{tasks.date}}</td>
            </tr>
{% endfor %}


        </table>
    </div>
</div>
            {% endblock %}