{% extends 'base.html' %}
{% block body %}
<div class="title">
    <p>التقارير</p>
</div>

<!-- قسم البحث والفلترة -->
<div class="search-section noprint" style="margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
    <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
        <div>
            <label for="clientSearch" style="font-weight: bold; margin-bottom: 5px; display: block;">البحث بـ اسم العميل:</label>
            <input type="text" id="clientSearch" placeholder="ادخل اسم العميل..." 
                   style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 200px;">
        </div>
        
        <div>
            <label for="reportType" style="font-weight: bold; margin-bottom: 5px; display: block;">نوع التقرير:</label>
            <select id="reportType" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 150px;">
                <option value="">جميع التقارير</option>
                <option value="مبيعات">مبيعات</option>
                <option value="مشتريات">مشتريات</option>
                <option value="أخرى">أخرى</option>
            </select>
        </div>
        
        <div>
            <label for="dateFrom" style="font-weight: bold; margin-bottom: 5px; display: block;">من تاريخ:</label>
            <input type="date" id="dateFrom" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        
        <div>
            <label for="dateTo" style="font-weight: bold; margin-bottom: 5px; display: block;">إلى تاريخ:</label>
            <input type="date" id="dateTo" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="searchReports()" style="background-color: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                🔍 بحث
            </button>
            <button onclick="resetSearch()" style="background-color: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                ↻ إعادة تعيين
            </button>
            <button onclick="printSelected()" style="background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
                🖨️ طباعة المحدد
            </button>
        </div>
    </div>
</div>

<div class="homalae-table" id="print">
    <input type="button" value="طباعة الكل" class="noprint" onclick="printDiv()" style="margin-bottom: 10px;"> 
    <table id="reportsTable">
        <tr>
            <th class="noprint">
                <input type="checkbox" id="selectAll" onchange="toggleAllRows()"> اختيار الكل
            </th>
            <th>رقم الفاتورة</th>
            <th>نوع التقرير</th>
            <th>العميل/المورد</th>
            <th>المجموع</th>
            <th>طريقة الاداء</th>
            <th>التاريخ</th>
        </tr>


        <!-- GET THIS FROM DB -->
        {% for task in task|reverse %}
        <tr class="report-row" data-client="{{ task.client|lower }}" data-type="{{ task.typeVA }}" data-date="{{ task.date }}">
            <td class="noprint">
                <input type="checkbox" class="row-select" value="{{ task.id }}">
            </td>
            <td>
                <a href="/factures/?fact=1&fact-id={{ task.id }}">
                    {{ task.id }}
                </a>
            </td>
            <td>{{ task.typeVA }}</td>
            <td>
                <a href="/factures/?clnt=1&clnt-id={{ task.client }}">
                    {{ task.client }}
                </a>
            </td>
            <td>{{ task.total }}</td>
            <td>{{ task.type_transaction }}</td>
            <td>{{ task.date }}</td>
        </tr>
        {% endfor %}
        <!-- DB END -->


    </table>
</div>

<style>
    /* تنسيق قسم البحث */
    .search-section {
        border: 2px solid var(--main);
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .search-section input, 
    .search-section select {
        font-size: 14px;
        transition: border-color 0.3s ease;
    }
    
    .search-section input:focus, 
    .search-section select:focus {
        border-color: var(--main);
        box-shadow: 0 0 5px rgba(22, 163, 57, 0.3);
        outline: none;
    }
    
    .search-section button {
        font-size: 14px;
        border-radius: 6px;
        transition: all 0.3s ease;
        font-weight: 600;
    }
    
    .search-section button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    /* تنسيق الجدول */
    .report-row.hidden-row {
        display: none !important;
    }
    
    .report-row:hover {
        background-color: #e8f5e8 !important;
    }
    
    /* تنسيق الـ checkboxes */
    .row-select, #selectAll {
        transform: scale(1.2);
        margin-left: 5px;
    }
    
    /* تنسيق للطباعة */
    @media print {
        @page {
            size: A4;
            margin: 1cm;
        }
        
        body {
            font-size: 14pt !important;
            line-height: 1.4 !important;
            color: black !important;
            background: white !important;
        }
        
        .search-section {
            display: none !important;
        }
        
        .noprint {
            display: none !important;
        }
        
        .report-row td:first-child {
            display: none !important;
        }
        
        .report-row th:first-child {
            display: none !important;
        }
        
        .title {
            margin-bottom: 30px !important;
            page-break-after: avoid;
        }
        
        .title p {
            font-size: 24pt !important;
            font-weight: bold !important;
            color: black !important;
            text-align: center !important;
        }
        
        .homalae-table {
            width: 100% !important;
            margin: 0 !important;
        }
        
        table {
            width: 100% !important;
            margin: 0 !important;
            border-collapse: collapse !important;
            font-size: 12pt !important;
        }
        
        th {
            background-color: #f0f0f0 !important;
            font-size: 13pt !important;
            font-weight: bold !important;
            padding: 8pt !important;
            border: 1px solid black !important;
            text-align: center !important;
            page-break-after: avoid;
        }
        
        td {
            padding: 6pt !important;
            border: 1px solid black !important;
            text-align: center !important;
            font-size: 11pt !important;
            page-break-inside: avoid;
        }
        
        tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }
        
        /* تحسين طباعة الروابط */
        a {
            color: black !important;
            text-decoration: none !important;
        }
        
        /* تحسين الأرقام والنصوص */
        td:first-child,
        td:last-child {
            font-weight: bold !important;
        }
        
        /* منع تقطيع الجدول */
        thead {
            display: table-header-group;
        }
        
        tbody {
            display: table-row-group;
        }
    }
    
    /* تحسين المظهر العام */
    .homalae-table input[type="button"] {
        background-color: var(--main);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.3s ease;
    }
    
    .homalae-table input[type="button"]:hover {
        background-color: var(--secondary);
    }
</style>

<script>
    // وظائف البحث والفلترة
    function searchReports() {
        const clientSearch = document.getElementById('clientSearch').value.toLowerCase();
        const reportType = document.getElementById('reportType').value;
        const dateFrom = document.getElementById('dateFrom').value;
        const dateTo = document.getElementById('dateTo').value;
        
        const rows = document.querySelectorAll('.report-row');
        let visibleCount = 0;
        
        rows.forEach(row => {
            const clientName = row.getAttribute('data-client');
            const type = row.getAttribute('data-type');
            const date = row.getAttribute('data-date');
            
            let showRow = true;
            
            // فلترة بـ اسم العميل
            if (clientSearch && !clientName.includes(clientSearch)) {
                showRow = false;
            }
            
            // فلترة بـ نوع التقرير
            if (reportType && type !== reportType) {
                showRow = false;
            }
            
            // فلترة بـ التاريخ من
            if (dateFrom && date < dateFrom) {
                showRow = false;
            }
            
            // فلترة بـ التاريخ إلى
            if (dateTo && date > dateTo) {
                showRow = false;
            }
            
            if (showRow) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });
        
        // عرض رسالة إذا لم توجد نتائج
        if (visibleCount === 0) {
            alert('لا توجد نتائج تطابق معايير البحث');
        }
    }
    
    // إعادة تعيين البحث
    function resetSearch() {
        document.getElementById('clientSearch').value = '';
        document.getElementById('reportType').value = '';
        document.getElementById('dateFrom').value = '';
        document.getElementById('dateTo').value = '';
        
        const rows = document.querySelectorAll('.report-row');
        rows.forEach(row => {
            row.style.display = '';
        });
        
        // إلغاء تحديد جميع الصفوف
        document.getElementById('selectAll').checked = false;
        const checkboxes = document.querySelectorAll('.row-select');
        checkboxes.forEach(cb => cb.checked = false);
    }
    
    // تحديد/إلغاء تحديد جميع الصفوف المرئية
    function toggleAllRows() {
        const selectAll = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.row-select');
        
        checkboxes.forEach(cb => {
            const row = cb.closest('.report-row');
            if (row.style.display !== 'none') {
                cb.checked = selectAll.checked;
            }
        });
    }
    
    // طباعة الفواتير المحددة
    function printSelected() {
        const selectedBoxes = document.querySelectorAll('.row-select:checked');
        
        if (selectedBoxes.length === 0) {
            alert('يرجى اختيار فاتورة واحدة على الأقل للطباعة');
            return;
        }
        
        // إخفاء الصفوف غير المحددة مؤقتاً
        const allRows = document.querySelectorAll('.report-row');
        const hiddenRows = [];
        
        allRows.forEach(row => {
            const checkbox = row.querySelector('.row-select');
            if (!checkbox.checked) {
                hiddenRows.push(row);
                row.style.display = 'none';
            }
        });
        
        // طباعة الجدول
        printDiv();
        
        // إعادة إظهار الصفوف المخفية
        hiddenRows.forEach(row => {
            row.style.display = '';
        });
    }
    
    // تطبيق البحث فوري عند الكتابة
    document.getElementById('clientSearch').addEventListener('input', function() {
        if (this.value.length >= 2 || this.value.length === 0) {
            searchReports();
        }
    });
    
    // البحث عند تغيير نوع التقرير
    document.getElementById('reportType').addEventListener('change', searchReports);
    
    // البحث عند تغيير التواريخ
    document.getElementById('dateFrom').addEventListener('change', searchReports);
    document.getElementById('dateTo').addEventListener('change', searchReports);
    
    // تحسين دالة الطباعة
    function printDiv() {
        // حفظ عنوان الصفحة الأصلي
        const originalTitle = document.title;
        
        // تغيير عنوان الصفحة للطباعة
        document.title = 'تقرير التقارير - ' + new Date().toLocaleDateString('ar-EG');
        
        // إعدادات الطباعة
        const printContents = document.getElementById('print').outerHTML;
        const titleContent = document.querySelector('.title').outerHTML;
        
        // إنشاء نافذة جديدة للطباعة
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>تقرير التقارير</title>
                <style>
                    @page {
                        size: A4;
                        margin: 1.5cm;
                    }
                    
                    body {
                        font-family: 'Arial', 'Tahoma', sans-serif;
                        font-size: 14pt;
                        line-height: 1.5;
                        color: black;
                        background: white;
                        margin: 0;
                        padding: 20px;
                        direction: rtl;
                    }
                    
                    .title {
                        text-align: center;
                        margin-bottom: 30px;
                        page-break-after: avoid;
                    }
                    
                    .title p {
                        font-size: 26pt;
                        font-weight: bold;
                        color: black;
                        margin: 0;
                        padding: 20px 0;
                        border-bottom: 3px solid #333;
                    }
                    
                    .homalae-table {
                        width: 100%;
                        margin: 0;
                    }
                    
                    table {
                        width: 100%;
                        border-collapse: collapse;
                        font-size: 13pt;
                        margin-top: 20px;
                    }
                    
                    th {
                        background-color: #e0e0e0 !important;
                        font-size: 14pt;
                        font-weight: bold;
                        padding: 10pt;
                        border: 2px solid black;
                        text-align: center;
                        page-break-after: avoid;
                    }
                    
                    td {
                        padding: 8pt;
                        border: 1px solid black;
                        text-align: center;
                        font-size: 12pt;
                        page-break-inside: avoid;
                    }
                    
                    tr {
                        page-break-inside: avoid;
                        page-break-after: auto;
                    }
                    
                    tr:nth-child(even) {
                        background-color: #f9f9f9 !important;
                    }
                    
                    a {
                        color: black;
                        text-decoration: none;
                        font-weight: bold;
                    }
                    
                    /* إخفاء عمود الاختيار */
                    .report-row td:first-child,
                    .report-row th:first-child {
                        display: none;
                    }
                    
                    thead {
                        display: table-header-group;
                    }
                    
                    tbody {
                        display: table-row-group;
                    }
                    
                    /* تذييل الصفحة */
                    .print-footer {
                        position: fixed;
                        bottom: 1cm;
                        left: 0;
                        right: 0;
                        text-align: center;
                        font-size: 10pt;
                        color: #666;
                        border-top: 1px solid #ccc;
                        padding-top: 10px;
                    }
                </style>
            </head>
            <body>
                ${titleContent}
                ${printContents}
                <div class="print-footer">
                    تاريخ الطباعة: ${new Date().toLocaleString('ar-EG')}
                </div>
            </body>
            </html>
        `);
        
        printWindow.document.close();
        
        // انتظار تحميل المحتوى ثم الطباعة
        setTimeout(() => {
            printWindow.focus();
            printWindow.print();
            printWindow.close();
            
            // استعادة العنوان الأصلي
            document.title = originalTitle;
        }, 500);
    }
</script>

            {% endblock %}