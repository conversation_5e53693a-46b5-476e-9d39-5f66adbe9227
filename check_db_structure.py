#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بنية قاعدة البيانات
"""

import sqlite3

def check_database_structure():
    try:
        conn = sqlite3.connect('database.db')
        conn.row_factory = sqlite3.Row
        
        print("📋 جداول قاعدة البيانات:")
        print("=" * 40)
        
        # عرض جميع الجداول
        tables = conn.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        """).fetchall()
        
        for table in tables:
            table_name = table['name']
            print(f"\n🗂️  الجدول: {table_name}")
            
            # عرض أعمدة كل جدول
            columns = conn.execute(f"PRAGMA table_info({table_name})").fetchall()
            for col in columns:
                print(f"   - {col['name']} ({col['type']})")
            
            # عرض عدد السجلات
            count = conn.execute(f"SELECT COUNT(*) as count FROM {table_name}").fetchone()
            print(f"   📊 عدد السجلات: {count['count']}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    check_database_structure()