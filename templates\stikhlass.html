{% extends 'base.html' %}
{% block body %}
<div class="title">
    <p>ورقة الإستخلاص</p>
</div>
<div class="form" style="  display: flex;
justify-content: center; margin-left: 5px;;">
    <form action="/stikhlass/" method="POST">
        <label>
            <span>إسم العميل : </span>
            <select name="client">
                {% for clients in clients %}
                <option>{{ clients.name }}</option>
                {% endfor %}
                {% for mowaridon in mowaridon %}
                <option>{{ mowaridon.name }}</option>
                {% endfor %}
            </select>
        <label>
            <input type="submit" value="إبحث عن الفاتورة" />
        </label>
    </form>
</div>

<div class="form">
    <form class="stikhlass" action="/stikhlassgen/" method="POST">
        <label>
            <span>إسم العميل : </span>
            <select name="client">
                {% for clients in clients %}
                    {% if clients.credit|int < 0 %}
                    <option>{{clients.name}}</option>
                    {% endif %}
                {% endfor %}
                {% for mowaridon in mowaridon %}
                {% if mowaridon.credit|int > 0 %}
                <option>{{ mowaridon.name }}</option>
                {% endif %}
                {% endfor %}
            </select>
        </label>
        <label>
            <span>المبلغ :</span>
            <input type="number" value="0" name="amount" />
        </label>
        <label>
            <span>التاريخ :
            </span>
            <input type="date" required name="date" id="dateInput" />
        </label>
        <label>
            <span>رقم الشيك :
            </span>
            <input type="number" required value="0" name="check" />
        </label>
        <label>
            <span>تسجيل :
            </span>
            <input type="submit" value="تسجيل ورقة الإستخلاص" />
        </label>
    </form>
</div>
    <div class="homalae-table">
        <table>
            <tr>
                <th>رقم الإستخلاص</th>
                <th>العميل/المورد</th>
                <th>رقم الشيك</th>
                <th>المبلغ المؤدى</th>
                <th>الدين المتبقي</th>
                <th>التاريخ</th>
            </tr>
    
    
            <!-- GET THIS FROM DB -->
            {% for stikhlas in stikhlas|reverse %}
            <tr>
                <td>{{ stikhlas.id }}</td>
                <td>{{ stikhlas.client }}</td>
                <td>{{ stikhlas.check }}</td>
                <td>{{ stikhlas.amount }}</td>
                <td>{{ stikhlas.credit }}</td>
                <td>{{ stikhlas.date }}</td>
            </tr>
            {% endfor %}
            <!-- DB END -->
    
    
        </table>
    </div>

{% endblock %}