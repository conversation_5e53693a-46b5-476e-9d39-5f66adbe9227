{% extends 'base.html' %}
{% block body %}
<div class="title">
    {% set vars = {'total': 0} %}
    {% for task in task %}
    {% if vars.update({'total' : vars.total|int + task.credit}) %} {% endif %}
    {% endfor %}
    <p>قائمة العملاء</p>
    <p>TOTAL = {{ vars.total }}</p>
</div>
<div class="form" style="padding-bottom: 1rem">
    <form action="/3omalae/" method="POST">
        <p class="form-title">إضافة عميل</p>
        <div class="first-line">
            <label>
                <span>الإسم :</span>
                <input type="text" required minlength="3" pattern="^[a-zA-Z0-9]{3,25}$" name="name" />
            </label>
            <label>
                <span>المدينة :</span>
                <input type="text" maxlength="25" pattern="^[a-zA-Z0-9]{0,25}$" name="city" />
            </label>
            <label>
                <span>الهاتف :</span>
                <input type="text" required minlength="10" pattern="^[0-9]*$" name="tele" />
            </label>
            <label>
                <span>التعريف :</span>
                <input type="text" pattern="^[a-zA-Z0-9]{0,10}$" name="cin" />
            </label>
            <label>
                <span>الوصف :</span>
                <input type="text" maxlength="200" pattern="^[a-zA-Z0-9]{0,200}$" name="description" />
            </label>
            <label>
                <span style="visibility: hidden;">الهاتف :</span>
                <input value="إضافة العميل" type="submit" />
            </label>
        </div>
        <input type='hidden' value='add' name="action"/>
    </form>
</div>
<div class="homalae-table" id="print">
    <input  type="button" value="الطباعة" class="noprint" onclick="printDiv()" > 
    <style>
        @media print {
           .noprint {
              visibility: hidden;
           }
        }
        </style>
    <table>
        <tr>
            <th>الإسم</th>
            <th>المدينة</th>
            <th>الهاتف</th>
            <th>الوصف</th>
            <th>البطاقة الوطنية</th>
            <th>المبلغ المأتمن</th>
        </tr>


        <!-- GET THIS FROM DB -->
        {% for task in task %}
        <tr>
            <td>
                <a href="/factures/?clnt=1&clnt-id={{ task.name }}">
                    {{ task.name }}
                </a>
            </td>
            <td>{{task.city}}</td>
            <td>{{task.tele}}</td>
            <td>{{task.description}}</td>
            <td>{{task.cin}}</td>
            <td>{{task.credit}} DH</td>
            <td>
                <form method='POST'>
                    <input type='hidden' value='{{ task.name }}' name="client" />
                    <input type='hidden' value='delete' name="action" />
                    <input type="submit" value='x'>
                </form>
            </td>
        </tr>
        {% endfor %}
        <!-- DB END -->


    </table>
</div>
            {% endblock %}