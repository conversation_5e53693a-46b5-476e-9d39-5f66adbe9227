{% extends 'base.html' %}
{% block body %}
<div class="top-bar">
    <div class="top-bar__close" onClick="closeTopBar()">
        <span></span>
        <span></span>
    </div>
    <p>!!! المرجو التأكد من كمية المنتوج قبل البيع  !!!</p>
</div>
<div class="title">
    <p>إضافة فاتورة للمبايعات</p>
</div>
<div class="form">
    <form method="POST" action="/genfacture/">
        <input type="hidden" id="typeVA" name="typeVA" value="V">
        <input type="hidden" id="totol" name="total" value="">
        <div class="first-line">
            <label>
                <span>رقم الفاتورة :</span>
                <input type="number" value={{ num|int + 1 }} required name="num-facture" />
            </label>
            <label>
                <span>التاريخ :</span>
                <input type="date" required name="date" id="dateInput" />
            </label>
            <label>
                <span>إسم العميل :</span>
                <select  name="id_client" class="js-client-select">
                    <option></option>
                    {% for task in tasks %}
                        <option>{{ task.name }}</option>
                    {% endfor %}
                </select>
            </label>
        </div>
        <div class="asnaf">
            <div class="senf">
                    <div class="senf-labels">
                        <label>
                            <span>الصنف :</span>
                            <select class="js-client-select" searchable="search" name="sinf_1">
                                <!-- GET THIS FROM DB -->
                                <option value=""></option>
                                {% for senf in senf %}
                                <option value="{{ senf.name }}">{{ senf.name }}</option>
                                {% endfor %}
                                <!-- DB END -->
                            </select>
                        </label>
                        <label>
                            <span>الكمية :</span>
                            <input onChange="updateTotal()" required type="number" value="0" name="amount-senf1" class="amount-senf1" />
                        </label>
                        <label>
                            <span>ثمن الوحدة :</span>
                            <input onChange="updateTotal()" required type="number" step=0.01  value="0" name="price-senf1" class="price-senf1" />
                        </label>
                        <div class="final-price">
                                <p>المجموع : <a class="total1">0</a>DH</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="asnaf">
                <div class="senf">
                    <div class="senf-labels">
                        <label>
                            <select class="js-client-select" name="sinf_2">
                                <!-- GET THIS FROM DB -->
                                <option value=""></option>
                                {% for senf in senf %}
                                <option value="{{ senf.name }}">{{ senf.name }}</option>
                                {% endfor %}
                                <!-- DB END -->
                            </select>
                        </label>
                        <label>
                            <input onChange="updateTotal()" required type="number" value="0" name="amount-senf2" class="amount-senf2" />
                        </label>
                        <label>
                            <input onChange="updateTotal()" required type="number" step=0.01  value="0" name="price-senf2" class="price-senf2" />
                        </label>
                        <div class="final-price">
                                <p>المجموع : <a class="total2">0</a>DH</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="asnaf">
                <div class="senf">
                    <div class="senf-labels">
                        <label>
                            <select class="js-client-select" name="sinf_3">
                                <!-- GET THIS FROM DB -->
                                <option value=""></option>
                                {% for senf in senf %}
                                <option value="{{ senf.name }}">{{ senf.name }}</option>
                                {% endfor %}
                                <!-- DB END -->
                            </select>
                        </label>
                        <label>
                            <input onChange="updateTotal()" required type="number" value="0" name="amount-senf3" class="amount-senf3" />
                        </label>
                        <label>
                            <input onChange="updateTotal()" required type="number" step=0.01  value="0" name="price-senf3" class="price-senf3" />
                        </label>
                        <div class="final-price">
                                <p>المجموع : <a class="total3">0</a>DH</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="asnaf">
                <div class="senf">
                    <div class="senf-labels">
                        <label>
                            <select class="js-client-select" name="sinf_4">
                                <!-- GET THIS FROM DB -->
                                <option value=""></option>
                                {% for senf in senf %}
                                <option value="{{ senf.name }}">{{ senf.name }}</option>
                                {% endfor %}
                                <!-- DB END -->
                            </select>
                        </label>
                        <label>
                            <input onChange="updateTotal()" required type="number" value="0" name="amount-senf4" class="amount-senf4" />
                        </label>
                        <label>
                            <input onChange="updateTotal()" required type="number"  step=0.01 value="0" name="price-senf4" class="price-senf4" />
                        </label>
                        <div class="final-price">
                                <p>المجموع : <a class="total4">0</a>DH</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="asnaf">
                <div class="senf">
                    <div class="senf-labels">
                        <label>
                            <select class="js-client-select" name="sinf_5">
                                <!-- GET THIS FROM DB -->
                                <option value=""></option>
                                {% for senf in senf %}
                                <option value="{{ senf.name }}">{{ senf.name }}</option>
                                {% endfor %}
                                <!-- DB END -->
                            </select>
                        </label>
                        <label>
                            <input onChange="updateTotal()" required type="number" value="0" name="amount-senf5" class="amount-senf5" />
                        </label>
                        <label>
                            <input onChange="updateTotal()" required type="number" step=0.01 value="0" name="price-senf5" class="price-senf5" />
                        </label>
                        <div class="final-price">
                                <p>المجموع : <a class="total5">0</a>DH</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="asnaf">
                <div class="senf">
                    <div class="senf-labels">
                        <label>
                            <select class="js-client-select" name="sinf_6">
                                <!-- GET THIS FROM DB -->
                                <option value=""></option>
                                {% for senf in senf %}
                                <option value="{{ senf.name }}">{{ senf.name }}</option>
                                {% endfor %}
                                <!-- DB END -->
                            </select>
                        </label>
                        <label>
                            <input onChange="updateTotal()" required type="number" value="0" name="amount-senf6" class="amount-senf6" />
                        </label>
                        <label>
                            <input onChange="updateTotal()" required type="number" step=0.01 value="0" name="price-senf6" class="price-senf6" />
                        </label>
                        <div class="final-price">
                                <p>المجموع : <a class="total6">0</a>DH</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="asnaf">
                <div class="senf">
                    <div class="senf-labels">
                        <label>
                            <select class="js-client-select" name="sinf_7">
                                <!-- GET THIS FROM DB -->
                                <option value=""></option>
                                {% for senf in senf %}
                                <option value="{{ senf.name }}">{{ senf.name }}</option>
                                {% endfor %}
                                <!-- DB END -->
                            </select>
                        </label>
                        <label>
                            <input onChange="updateTotal()" required type="number" value="0" name="amount-senf7" class="amount-senf7" />
                        </label>
                        <label>
                            <input onChange="updateTotal()" required type="number" step=0.01 value="0" name="price-senf7" class="price-senf7" />
                        </label>
                        <div class="final-price">
                                <p>المجموع : <a class="total7">0</a>DH</p>
                        </div>
                    </div>
                </div>
            </div>
            
        <div class="final-line">
            <div class="flex-container">
                <div class="final-price">
                    <p>الثمن النهائي : <a class="total">0</a>DH</p>
                </div>
                <div class="payment-method">
                    <label>
                        <span>طريقة الدفع :</span>
                        <select name="paymethod">
                            <option>cash</option>
                            <option>credit</option>
                        </select>
                    </label>
                </div>
            </div>
            <div class="submit-btn">
                <input type="submit" value="تسجيل الفاتورة" />
            </div>
        </div>
    </form>
</div>
            {% endblock %}