# نظام إدارة الفواتير والتقارير السنوية

## 📝 وصف المشروع
نظام ويب لإدارة الفواتير والتقارير السنوية باستخدام Flask و SQLite، يدعم اللغة العربية بالكامل.

## 🚀 كيفية التشغيل

### الطريقة السريعة
```bash
python start_server.py
```

### الطريقة اليدوية
1. **إنشاء قاعدة البيانات** (إذا لم تكن موجودة):
```bash
python create_database.py
```

2. **تشغيل الخادم**:
```bash
python app.py
```

3. **فتح المتصفح والذهاب إلى**:
   - الصفحة الرئيسية: http://localhost:5000
   - التقرير السنوي: http://localhost:5000/taqrirsanawi/

## 🧪 اختبار النظام

### اختبار البيانات الأساسية
```bash
python test_data.py
```

### اختبار جميع الوظائف
```bash
python test_all_functions.py
```

### فحص بنية قاعدة البيانات
```bash
python check_db_structure.py
```

## 📊 المميزات

### ✅ الوظائف المتاحة
- 👥 إدارة العملاء والموردين
- 🧾 فواتير البيع والشراء
- 💰 فواتير الاستخلاص
- 📈 التقارير السنوية
- 🔍 البحث بالعميل والسنة
- 📋 عرض جميع الفواتير

### 📋 بنية قاعدة البيانات
- **client**: جدول العملاء
- **mowarid**: جدول الموردين  
- **logs**: جدول الفواتير (البيع والشراء)
- **stikhlaslog**: جدول فواتير الاستخلاص

## 📂 ملفات المشروع

### الملفات الرئيسية
- `app.py` - التطبيق الرئيسي (Flask)
- `create_database.py` - إنشاء قاعدة البيانات
- `start_server.py` - بدء تشغيل الخادم

### ملفات الاختبار
- `test_data.py` - اختبار البيانات الأساسية
- `test_all_functions.py` - اختبار جميع الوظائف
- `check_db_structure.py` - فحص بنية قاعدة البيانات

### المجلدات
- `templates/` - قوالب HTML
- `static/` - ملفات CSS و JavaScript

## 🎯 استخدام التقرير السنوي

1. **اختيار العميل**: من القائمة المنسدلة
2. **اختيار السنة**: (اختياري) لتصفية النتائج
3. **عرض جميع الفواتير**: اختر "جميع المبيعات"
4. **عرض النتائج**: 
   - فواتير البيع
   - فواتير الاستخلاص  
   - الرصيد المتبقي

## 📈 البيانات التجريبية

يتم إنشاء البيانات التجريبية تلقائياً وتشمل:
- 10 عملاء
- 8 موردين
- 130 فاتورة بيع (50 لعام 2023، 80 لعام 2024)
- 75 فاتورة استخلاص (30 لعام 2023، 45 لعام 2024)

## ⚠️ ملاحظات هامة

- تأكد من وجود Python 3.6 أو أحدث
- قم بتثبيت Flask: `pip install flask`
- يتم حفظ قاعدة البيانات في `database.db`
- جميع التواريخ بصيغة YYYY-MM-DD

## 🐛 حل المشاكل الشائعة

### قاعدة البيانات فارغة
```bash
python create_database.py
```

### خطأ "no such table"
```bash
rm database.db
python create_database.py
```

### فحص الجداول المتاحة
```bash
python check_db_structure.py
```

## 📞 الدعم
إذا واجهت أي مشاكل، قم بتشغيل:
```bash
python test_all_functions.py
```
لفحص جميع الوظائف والتأكد من عملها.