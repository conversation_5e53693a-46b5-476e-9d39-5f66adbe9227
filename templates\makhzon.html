{% extends 'base.html' %}
{% block body %}
{% if pid %}
<div class="top-bar" style="z-index: 50;">
    <div class="top-bar__close" onClick="closeTopBar()">
        <span></span>
        <span></span>
    </div>
    {% for pid in pid %}
    <p>!!! {{pid.name}} = {{pid.quantity}} !!!</p>
    {% endfor %}
</div>
{% endif %}
<div class="title">
    <p>المخزون الحالي</p>
    {% set vars = {'total': 0|float} %}
    {% for task in task %}
    {% if vars.update({'total' : vars.total|float + task.total|float}) %} {% endif %}
    {% endfor %}
    <p>TOTAL = {{ vars.total }}</p>
</div>
<div class="form " style="padding-bottom: 1rem">
    <form action="/makhzon/" method="POST">
        <input type="hidden" name="type" value="add">
        <p class="form-title">إضافة منتوج</p>
        <div class="first-line">
            <label>
                <span>إسم المنتوج :</span>
                <input type="text" required minlength="3" maxlength="25" pattern="^[a-zA-Z0-9]{3,25}$" name="product" />
            </label>
            <label>
                <span>الكمية :</span>
                <input type="number" required min="0" name="quantity" />
            </label>
            <label>
                <input style="width: 10rem" value="إضافة المنتوج" type="submit" />
            </label>
        </div>
    </form>
</div>
<div class="form" style="padding-bottom: 1rem">
    <form action="/makhzon/" method="POST">
        <input type="hidden" name="type" value="search">

        <div class="first-line">
            
            <label>
                <span>إسم المنتوج :</span>
                <select class="js-client-select" name="product">
                    <option>produit</option>
                    {% for task in task %}
                        <option>{{ task.name }}</option>
                    {% endfor %}
                </select>
            </label>
            <label></label>
            <label>
                <input style="width: 10rem" value="البحث عن المنتوج" type="submit" />
            </label>
        </div>
    </form>
</div>
<div class="makhzon-table" id="print">
    <style>
        @media print {
           .noprint {
              visibility: hidden;
           }
        }
        </style>
    <input  type="button" value="الطباعة" class="noprint" onclick="printDiv()" > 
    <table>
        <tr>
            <th>إسم المنتوج</th>
            <th>الكمية المتبقية</th>
            <th>المجموع</th>
            <th></th>
        </tr>


        <!-- GET THIS FROM DB -->
        {% for task in task2 %}
        <tr>
            <td> {{ task.name }} </td>
            <td> {{ task.quantity }} </td>
            <td> {{ task.total }} </td>
            <td>
                 <form action="/makhzon/" method="post">
                <input type="hidden" name="type" value="remove">
                <button class="noprint">
                    <input type="submit"value="حذف" style="background: none; color: white; cursor: pointer; outline: none; border: none" />
                </button>
                <input type="hidden" name="item" value="{{ task.name }}">
            </form>
         </td>
        </tr>
        {% endfor %}
        <!-- DB END -->


    </table>
</div>
            {% endblock %}