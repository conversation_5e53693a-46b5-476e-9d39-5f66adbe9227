#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بدء تشغيل الخادم
"""

import os
import subprocess
import sys
import webbrowser
import time

def start_server():
    """بدء تشغيل خادم Flask"""
    
    print("🚀 بدء تشغيل خادم التطبيق...")
    print("=" * 40)
    
    # التأكد من وجود قاعدة البيانات
    if not os.path.exists('database.db'):
        print("⚠️  قاعدة البيانات غير موجودة...")
        print("📊 إنشاء قاعدة البيانات...")
        
        try:
            subprocess.run([sys.executable, 'create_database.py'], check=True)
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
        except subprocess.CalledProcessError:
            print("❌ فشل في إنشاء قاعدة البيانات")
            return
    
    print("🌐 بدء تشغيل الخادم...")
    print("📋 الروابط المتاحة:")
    print("   الصفحة الرئيسية: http://localhost:5000")
    print("   التقرير السنوي: http://localhost:5000/taqrirsanawi/")
    print("\n⏹️  لإيقاف الخادم: اضغط Ctrl+C")
    print("-" * 50)
    
    # فتح المتصفح تلقائياً
    time.sleep(2)
    try:
        webbrowser.open('http://localhost:5000/taqrirsanawi/')
    except:
        pass
    
    # بدء تشغيل Flask
    try:
        subprocess.run([sys.executable, 'app.py'])
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == "__main__":
    start_server()