#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار البيانات من قاعدة البيانات
"""

import sqlite3

def test_database():
    conn = sqlite3.connect('database.db')
    conn.row_factory = sqlite3.Row
    
    print("🔍 اختبار البيانات من قاعدة البيانات")
    print("=" * 50)
    
    # 1. عرض العملاء
    print("\n👥 العملاء الموجودون:")
    clients = conn.execute('SELECT name FROM client ORDER BY name LIMIT 10').fetchall()
    for i, client in enumerate(clients, 1):
        print(f"  {i}. {client['name']}")
    
    # 2. عرض فواتير البيع
    print(f"\n🧾 فواتير البيع (آخر 5 فواتير):")
    sales = conn.execute("""
        SELECT id, client, total, date 
        FROM logs 
        WHERE typeVA = 'V' 
        ORDER BY id DESC 
        LIMIT 5
    """).fetchall()
    
    for sale in sales:
        print(f"  رقم: {sale['id']} | العميل: {sale['client']} | المبلغ: {sale['total']} | التاريخ: {sale['date']}")
    
    # 3. عرض فواتير الاستخلاص
    print(f"\n💰 فواتير الاستخلاص (آخر 5 فواتير):")
    extractions = conn.execute("""
        SELECT id, client, amount, date 
        FROM stikhlaslog 
        ORDER BY id DESC 
        LIMIT 5
    """).fetchall()
    
    for extraction in extractions:
        print(f"  رقم: {extraction['id']} | العميل: {extraction['client']} | المبلغ: {extraction['amount']} | التاريخ: {extraction['date']}")
    
    # 4. اختبار بيانات عميل محدد
    print(f"\n🎯 اختبار بيانات عميل محدد:")
    first_client = clients[0]['name'] if clients else None
    
    if first_client:
        print(f"العميل المختار: {first_client}")
        
        # فواتير البيع
        client_sales = conn.execute("""
            SELECT COUNT(*) as count, SUM(total) as total
            FROM logs 
            WHERE client = ? AND typeVA = 'V'
        """, (first_client,)).fetchone()
        
        print(f"  📊 فواتير البيع: {client_sales['count']} فاتورة، المجموع: {client_sales['total'] or 0}")
        
        # فواتير الاستخلاص
        client_extractions = conn.execute("""
            SELECT COUNT(*) as count, SUM(amount) as total
            FROM stikhlaslog 
            WHERE client = ?
        """, (first_client,)).fetchone()
        
        print(f"  💰 فواتير الاستخلاص: {client_extractions['count']} فاتورة، المجموع: {client_extractions['total'] or 0}")
        
        # الرصيد
        remaining = (client_sales['total'] or 0) - (client_extractions['total'] or 0)
        print(f"  🏦 الدين المتبقي: {remaining}")
    
    # 5. إحصائيات عامة
    print(f"\n📈 إحصائيات عامة:")
    
    total_sales = conn.execute("SELECT COUNT(*) as count, SUM(total) as total FROM logs WHERE typeVA = 'V'").fetchone()
    print(f"  إجمالي فواتير البيع: {total_sales['count']} فاتورة، المجموع: {total_sales['total'] or 0}")
    
    total_extractions = conn.execute("SELECT COUNT(*) as count, SUM(amount) as total FROM stikhlaslog").fetchone()
    print(f"  إجمالي فواتير الاستخلاص: {total_extractions['count']} فاتورة، المجموع: {total_extractions['total'] or 0}")
    
    conn.close()

if __name__ == "__main__":
    test_database()