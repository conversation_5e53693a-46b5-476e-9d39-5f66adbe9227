#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء قاعدة البيانات مع بيانات تجريبية
"""

import sqlite3
from datetime import datetime, timedelta
import random

def create_database_with_sample_data():
    """إنشاء قاعدة البيانات وإدراج بيانات تجريبية"""
    
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    
    print("🔨 إنشاء قاعدة البيانات...")
    
    # 1. إنشاء جدول العملاء
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS client (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            city TEXT,
            tele TEXT,
            cin TEXT,
            description TEXT,
            credit REAL DEFAULT 0,
            created_date TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 2. إنشاء جدول الموردين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS mowarid (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            address TEXT,
            phone TEXT,
            created_date TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 3. إنشاء جدول الفواتير
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client TEXT NOT NULL,
            total REAL NOT NULL DEFAULT 0,
            date TEXT NOT NULL,
            typeVA TEXT NOT NULL,
            description TEXT,
            created_date TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 4. إنشاء جدول فواتير الاستخلاص
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS stikhlaslog (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client TEXT NOT NULL,
            amount REAL NOT NULL DEFAULT 0,
            date TEXT NOT NULL,
            description TEXT,
            created_date TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 5. إدراج بيانات العملاء التجريبية
    clients_data = [
        ('شركة النور للتجارة', 'الدار البيضاء', '0612345678', 'IC123456', 'شركة تجارة عامة', 15000),
        ('مؤسسة الأمل', 'الرباط', '0623456789', 'IC234567', 'مؤسسة خدمات', 8000),
        ('شركة الرحمن للصناعة', 'طنجة', '0634567890', 'IC345678', 'شركة صناعية', 25000),
        ('متجر البركة', 'فاس', '0645678901', 'IC456789', 'متجر تجزئة', 5000),
        ('شركة الفلاح', 'مراكش', '0656789012', 'IC567890', 'شركة زراعية', 12000),
        ('مؤسسة النجاح', 'أكادير', '0667890123', 'IC678901', 'مؤسسة تعليمية', 7500),
        ('شركة الخير للتوزيع', 'وجدة', '0678901234', 'IC789012', 'شركة توزيع', 18000),
        ('متجر الوفاء', 'تطوان', '0689012345', 'IC890123', 'متجر ملابس', 6000),
        ('شركة الأمان', 'الجديدة', '0690123456', 'IC901234', 'شركة أمنية', 20000),
        ('مؤسسة التقدم', 'خريبكة', '0601234567', 'IC012345', 'مؤسسة تقنية', 9500)
    ]
    
    for client_data in clients_data:
        cursor.execute('''
            INSERT OR IGNORE INTO client (name, city, tele, cin, description, credit) 
            VALUES (?, ?, ?, ?, ?, ?)
        ''', client_data)
    
    # 6. إدراج بيانات الموردين التجريبية
    suppliers = [
        'مصنع الحديد والصلب',
        'شركة الخشب المحدودة',
        'مصنع البلاستيك',
        'شركة الألمنيوم',
        'مصنع الإسمنت',
        'شركة المواد الكيماوية',
        'مصنع الورق',
        'شركة المعادن'
    ]
    
    for supplier in suppliers:
        cursor.execute('INSERT OR IGNORE INTO mowarid (name) VALUES (?)', (supplier,))
    
    # 7. إدراج فواتير البيع التجريبية
    print("📊 إنشاء فواتير البيع التجريبية...")
    
    # قائمة أسماء العملاء للاستخدام في الفواتير
    client_names = [client[0] for client in clients_data]
    
    # فواتير لعام 2023
    for i in range(50):
        client = random.choice(client_names)
        # تواريخ عام 2023
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 12, 31)
        random_date = start_date + timedelta(
            days=random.randint(0, (end_date - start_date).days)
        )
        date_str = random_date.strftime('%Y-%m-%d')
        total = round(random.uniform(1000, 50000), 2)
        
        cursor.execute('''
            INSERT INTO logs (client, total, date, typeVA, description)
            VALUES (?, ?, ?, 'V', 'فاتورة بيع')
        ''', (client, total, date_str))
    
    # فواتير لعام 2024
    for i in range(80):
        client = random.choice(client_names)
        # تواريخ عام 2024
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 12, 31)
        random_date = start_date + timedelta(
            days=random.randint(0, min((end_date - start_date).days, (datetime.now() - start_date).days))
        )
        date_str = random_date.strftime('%Y-%m-%d')
        total = round(random.uniform(1000, 50000), 2)
        
        cursor.execute('''
            INSERT INTO logs (client, total, date, typeVA, description)
            VALUES (?, ?, ?, 'V', 'فاتورة بيع')
        ''', (client, total, date_str))
    
    # 8. إدراج فواتير الاستخلاص التجريبية
    print("💰 إنشاء فواتير الاستخلاص التجريبية...")
    
    # فواتير استخلاص لعام 2023
    for i in range(30):
        client = random.choice(client_names)
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 12, 31)
        random_date = start_date + timedelta(
            days=random.randint(0, (end_date - start_date).days)
        )
        date_str = random_date.strftime('%Y-%m-%d')
        amount = round(random.uniform(500, 25000), 2)
        
        cursor.execute('''
            INSERT INTO stikhlaslog (client, amount, date, description)
            VALUES (?, ?, ?, 'استخلاص دفعة')
        ''', (client, amount, date_str))
    
    # فواتير استخلاص لعام 2024
    for i in range(45):
        client = random.choice(client_names)
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 12, 31)
        random_date = start_date + timedelta(
            days=random.randint(0, min((end_date - start_date).days, (datetime.now() - start_date).days))
        )
        date_str = random_date.strftime('%Y-%m-%d')
        amount = round(random.uniform(500, 25000), 2)
        
        cursor.execute('''
            INSERT INTO stikhlaslog (client, amount, date, description)
            VALUES (?, ?, ?, 'استخلاص دفعة')
        ''', (client, amount, date_str))
    
    # حفظ التغييرات
    conn.commit()
    
    # عرض إحصائيات
    print("\n✅ تم إنشاء قاعدة البيانات بنجاح!")
    print("=" * 40)
    
    clients_count = cursor.execute('SELECT COUNT(*) FROM client').fetchone()[0]
    suppliers_count = cursor.execute('SELECT COUNT(*) FROM mowarid').fetchone()[0]
    sales_count = cursor.execute('SELECT COUNT(*) FROM logs WHERE typeVA = "V"').fetchone()[0]
    extractions_count = cursor.execute('SELECT COUNT(*) FROM stikhlaslog').fetchone()[0]
    
    print(f"👥 العملاء: {clients_count}")
    print(f"🏭 الموردين: {suppliers_count}")
    print(f"🧾 فواتير البيع: {sales_count}")
    print(f"💰 فواتير الاستخلاص: {extractions_count}")
    
    conn.close()

if __name__ == "__main__":
    create_database_with_sample_data()