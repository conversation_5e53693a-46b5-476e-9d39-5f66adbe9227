#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جميع وظائف التطبيق
"""

import sqlite3
from app import get_clients, get_suppliers, get_sales_data, get_extraction_data, get_all_sales_data, get_all_extraction_data

def test_all_functions():
    """اختبار جميع الوظائف"""
    
    print("🧪 اختبار جميع وظائف التطبيق")
    print("=" * 50)
    
    # 1. اختبار جلب العملاء
    print("\n👥 اختبار جلب العملاء:")
    clients = get_clients()
    if clients:
        print(f"✅ تم جلب {len(clients)} عميل")
        print(f"   أول عميل: {clients[0]['name']}")
    else:
        print("❌ فشل في جلب العملاء")
    
    # 2. اختبار جلب الموردين
    print("\n🏭 اختبار جلب الموردين:")
    suppliers = get_suppliers()
    if suppliers:
        print(f"✅ تم جلب {len(suppliers)} مورد")
        print(f"   أول مورد: {suppliers[0]['name']}")
    else:
        print("❌ فشل في جلب الموردين")
    
    # 3. اختبار جلب فواتير البيع لعميل محدد
    if clients:
        client_name = clients[0]['name']
        print(f"\n🧾 اختبار فواتير البيع للعميل: {client_name}")
        
        # بدون سنة محددة
        sales = get_sales_data(client_name)
        print(f"✅ فواتير البيع (جميع السنوات): {len(sales)} فاتورة")
        if sales:
            total = sum(sale['total'] for sale in sales)
            print(f"   المجموع: {total}")
        
        # لسنة 2024
        sales_2024 = get_sales_data(client_name, '2024')
        print(f"✅ فواتير البيع (2024): {len(sales_2024)} فاتورة")
        if sales_2024:
            total_2024 = sum(sale['total'] for sale in sales_2024)
            print(f"   المجموع: {total_2024}")
        
        # لسنة 2023
        sales_2023 = get_sales_data(client_name, '2023')
        print(f"✅ فواتير البيع (2023): {len(sales_2023)} فاتورة")
        if sales_2023:
            total_2023 = sum(sale['total'] for sale in sales_2023)
            print(f"   المجموع: {total_2023}")
    
    # 4. اختبار جلب فواتير الاستخلاص لعميل محدد
    if clients:
        print(f"\n💰 اختبار فواتير الاستخلاص للعميل: {client_name}")
        
        # بدون سنة محددة
        extractions = get_extraction_data(client_name)
        print(f"✅ فواتير الاستخلاص (جميع السنوات): {len(extractions)} فاتورة")
        if extractions:
            total = sum(ex['total'] for ex in extractions)
            print(f"   المجموع: {total}")
        
        # لسنة 2024
        extractions_2024 = get_extraction_data(client_name, '2024')
        print(f"✅ فواتير الاستخلاص (2024): {len(extractions_2024)} فاتورة")
        if extractions_2024:
            total_2024 = sum(ex['total'] for ex in extractions_2024)
            print(f"   المجموع: {total_2024}")
        
        # لسنة 2023
        extractions_2023 = get_extraction_data(client_name, '2023')
        print(f"✅ فواتير الاستخلاص (2023): {len(extractions_2023)} فاتورة")
        if extractions_2023:
            total_2023 = sum(ex['total'] for ex in extractions_2023)
            print(f"   المجموع: {total_2023}")
    
    # 5. اختبار جلب جميع فواتير البيع
    print(f"\n📊 اختبار جميع فواتير البيع:")
    
    all_sales = get_all_sales_data()
    print(f"✅ جميع فواتير البيع: {len(all_sales)} فاتورة")
    if all_sales:
        total = sum(sale['total'] for sale in all_sales)
        print(f"   المجموع الكلي: {total}")
    
    all_sales_2024 = get_all_sales_data('2024')
    print(f"✅ فواتير البيع (2024): {len(all_sales_2024)} فاتورة")
    if all_sales_2024:
        total_2024 = sum(sale['total'] for sale in all_sales_2024)
        print(f"   المجموع: {total_2024}")
    
    all_sales_2023 = get_all_sales_data('2023')
    print(f"✅ فواتير البيع (2023): {len(all_sales_2023)} فاتورة")
    if all_sales_2023:
        total_2023 = sum(sale['total'] for sale in all_sales_2023)
        print(f"   المجموع: {total_2023}")
    
    # 6. اختبار جلب جميع فواتير الاستخلاص
    print(f"\n💸 اختبار جميع فواتير الاستخلاص:")
    
    all_extractions = get_all_extraction_data()
    print(f"✅ جميع فواتير الاستخلاص: {len(all_extractions)} فاتورة")
    if all_extractions:
        total = sum(ex['total'] for ex in all_extractions)
        print(f"   المجموع الكلي: {total}")
    
    all_extractions_2024 = get_all_extraction_data('2024')
    print(f"✅ فواتير الاستخلاص (2024): {len(all_extractions_2024)} فاتورة")
    if all_extractions_2024:
        total_2024 = sum(ex['total'] for ex in all_extractions_2024)
        print(f"   المجموع: {total_2024}")
    
    all_extractions_2023 = get_all_extraction_data('2023')
    print(f"✅ فواتير الاستخلاص (2023): {len(all_extractions_2023)} فاتورة")
    if all_extractions_2023:
        total_2023 = sum(ex['total'] for ex in all_extractions_2023)
        print(f"   المجموع: {total_2023}")
    
    # 7. ملخص النتائج
    print(f"\n🎯 ملخص الاختبار:")
    print("=" * 30)
    
    if clients and suppliers and all_sales and all_extractions:
        print("✅ جميع الوظائف تعمل بشكل صحيح!")
        
        print(f"\n📋 الإحصائيات النهائية:")
        print(f"   👥 العملاء: {len(clients)}")
        print(f"   🏭 الموردين: {len(suppliers)}")
        print(f"   🧾 فواتير البيع: {len(all_sales)}")
        print(f"   💰 فواتير الاستخلاص: {len(all_extractions)}")
        
        total_sales_amount = sum(sale['total'] for sale in all_sales)
        total_extractions_amount = sum(ex['total'] for ex in all_extractions)
        remaining_debt = total_sales_amount - total_extractions_amount
        
        print(f"   💵 إجمالي المبيعات: {total_sales_amount:,.2f}")
        print(f"   💸 إجمالي الاستخلاص: {total_extractions_amount:,.2f}")
        print(f"   🏦 إجمالي الديون المتبقية: {remaining_debt:,.2f}")
        
    else:
        print("❌ بعض الوظائف لا تعمل بشكل صحيح")
    
    print(f"\n🌐 لاختبار واجهة الويب:")
    print(f"   http://localhost:5000")
    print(f"   http://localhost:5000/taqrirsanawi/")

if __name__ == "__main__":
    test_all_functions()