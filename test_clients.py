#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار صفحة العملاء
"""

import sqlite3

def test_clients_data():
    """اختبار بيانات العملاء"""
    
    print("👥 اختبار بيانات العملاء")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('database.db')
        conn.row_factory = sqlite3.Row
        
        # جلب جميع العملاء
        clients = conn.execute('''
            SELECT name, city, tele, cin, description, credit
            FROM client 
            ORDER BY name
        ''').fetchall()
        
        print(f"📊 العملاء الموجودين: {len(clients)}")
        print("-" * 40)
        
        for i, client in enumerate(clients, 1):
            client_name = client['name']
            
            # حساب إجمالي المبيعات
            total_sales = conn.execute('''
                SELECT COALESCE(SUM(total), 0) as total
                FROM logs 
                WHERE client = ? AND typeVA = 'V'
            ''', (client_name,)).fetchone()['total']
            
            # حساب إجمالي الاستخلاص
            total_extractions = conn.execute('''
                SELECT COALESCE(SUM(amount), 0) as total
                FROM stikhlaslog 
                WHERE client = ?
            ''', (client_name,)).fetchone()['total']
            
            # حساب الرصيد المتبقي
            balance = total_sales - total_extractions
            
            print(f"{i:2d}. {client_name}")
            print(f"    📍 المدينة: {client['city'] or 'غير محدد'}")
            print(f"    📞 الهاتف: {client['tele'] or 'غير محدد'}")
            print(f"    🆔 التعريف: {client['cin'] or 'غير محدد'}")
            print(f"    📝 الوصف: {client['description'] or 'غير محدد'}")
            print(f"    💰 المبيعات: {total_sales:,.2f}")
            print(f"    💸 الاستخلاص: {total_extractions:,.2f}")
            print(f"    🏦 الرصيد: {balance:,.2f}")
            print("-" * 40)
        
        conn.close()
        
        print("✅ اختبار العملاء اكتمل بنجاح!")
        print("🌐 لعرض صفحة العملاء:")
        print("   http://localhost:5000/3omalae/")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العملاء: {e}")

if __name__ == "__main__":
    test_clients_data()